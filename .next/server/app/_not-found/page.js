/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=private-next-app-dir%2Fnot-found.tsx&appDir=%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=private-next-app-dir%2Fnot-found.tsx&appDir=%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.tsx */ \"(rsc)/./src/app/not-found.tsx\")),\n                \"/Users/<USER>/Documents/NoiseSleep/src/app/not-found.tsx\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Documents/NoiseSleep/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.tsx */ \"(rsc)/./src/app/not-found.tsx\")), \"/Users/<USER>/Documents/NoiseSleep/src/app/not-found.tsx\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/_not-found/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=private-next-app-dir%2Fnot-found.tsx&appDir=%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fapp%2Fnot-found.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fapp%2Fnot-found.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.tsx */ \"(ssr)/./src/app/not-found.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGb19vJTJGRG9jdW1lbnRzJTJGTm9pc2VTbGVlcCUyRnNyYyUyRmFwcCUyRm5vdC1mb3VuZC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBKQUEwRiIsInNvdXJjZXMiOlsid2VicGFjazovL25vaXNlc2xlZXAtd2ViLz83YTYxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL29fby9Eb2N1bWVudHMvTm9pc2VTbGVlcC9zcmMvYXBwL25vdC1mb3VuZC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fapp%2Fnot-found.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/not-found.tsx":
/*!*******************************!*\
  !*** ./src/app/not-found.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _utils_i18nHelpers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/i18nHelpers */ \"(ssr)/./src/utils/i18nHelpers.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n/**\n * 全局404页面\n * 当用户访问不存在的页面时显示\n */ function NotFound() {\n    // 从路径中检测语言\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const locale = pathname.startsWith(\"/zh\") ? \"zh\" : \"en\";\n    const content = (0,_utils_i18nHelpers__WEBPACK_IMPORTED_MODULE_1__.getErrorPageContent)(locale, \"404\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full mx-auto text-center px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-24 h-24 mx-auto mb-6 text-gray-400 dark:text-gray-600\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        fill: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/not-found.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/not-found.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/not-found.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-6xl font-bold text-gray-900 dark:text-gray-100 mb-4\",\n                    children: \"404\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/not-found.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-4\",\n                    children: content.title\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/not-found.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 dark:text-gray-400 mb-8\",\n                    children: content.message\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/not-found.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: locale === \"zh\" ? \"/zh\" : \"/\",\n                            className: \"inline-block w-full px-6 py-3 bg-indigo-500 text-white rounded-lg font-medium hover:bg-indigo-600 transition-colors\",\n                            children: content.action\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/not-found.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: locale === \"zh\" ? \"/zh/sounds\" : \"/sounds\",\n                                    className: \"flex-1 px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-lg font-medium hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors\",\n                                    children: locale === \"zh\" ? \"浏览音频\" : \"Browse Sounds\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/not-found.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: locale === \"zh\" ? \"/\" : \"/zh\",\n                                    className: \"flex-1 px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-lg font-medium hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors\",\n                                    children: locale === \"zh\" ? \"English\" : \"中文版\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/not-found.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/not-found.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/not-found.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 text-sm text-gray-500 dark:text-gray-400\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: locale === \"zh\" ? \"您可能在寻找：\" : \"You might be looking for:\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/not-found.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-2 space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: locale === \"zh\" ? \"/zh/sounds/rain\" : \"/sounds/rain\",\n                                    className: \"text-indigo-500 hover:text-indigo-600\",\n                                    children: locale === \"zh\" ? \"雨声\" : \"Rain Sounds\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/not-found.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: locale === \"zh\" ? \"/zh/sounds/nature\" : \"/sounds/nature\",\n                                    className: \"text-indigo-500 hover:text-indigo-600\",\n                                    children: locale === \"zh\" ? \"自然音\" : \"Nature Sounds\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/not-found.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: locale === \"zh\" ? \"/zh/mixing\" : \"/mixing\",\n                                    className: \"text-indigo-500 hover:text-indigo-600\",\n                                    children: locale === \"zh\" ? \"混音器\" : \"Audio Mixer\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/not-found.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/not-found.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/not-found.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/not-found.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/not-found.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/not-found.tsx\n");

/***/ }),

/***/ "(ssr)/./src/utils/i18nHelpers.ts":
/*!**********************************!*\
  !*** ./src/utils/i18nHelpers.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAudioErrorMessage: () => (/* binding */ getAudioErrorMessage),\n/* harmony export */   getErrorPageContent: () => (/* binding */ getErrorPageContent),\n/* harmony export */   getLocalizedText: () => (/* binding */ getLocalizedText),\n/* harmony export */   handleLanguageSwitchError: () => (/* binding */ handleLanguageSwitchError),\n/* harmony export */   hasTranslationKey: () => (/* binding */ hasTranslationKey),\n/* harmony export */   logErrorBoundaryError: () => (/* binding */ logErrorBoundaryError),\n/* harmony export */   safeTranslate: () => (/* binding */ safeTranslate)\n/* harmony export */ });\n/**\n * 国际化辅助工具\n * 提供翻译键缺失时的优雅降级机制\n */ /**\n * 安全的翻译函数，提供回退机制\n * @param t - next-intl 翻译函数\n * @param key - 翻译键\n * @param fallback - 回退文本\n * @param params - 翻译参数\n * @returns 翻译文本或回退文本\n */ function safeTranslate(t, key, fallback, params) {\n    try {\n        const result = t(key, params);\n        // 检查是否返回了缺失消息标识\n        if (result.includes(\"MISSING_MESSAGE\") || result === key) {\n            console.warn(`Translation missing for key: ${key}`);\n            return fallback || key;\n        }\n        return result;\n    } catch (error) {\n        console.error(`Translation error for key: ${key}`, error);\n        return fallback || key;\n    }\n}\n/**\n * 获取本地化文本的安全方法\n * @param textObj - 包含多语言文本的对象\n * @param locale - 当前语言环境\n * @param fallback - 回退文本\n * @returns 本地化文本\n */ function getLocalizedText(textObj, locale, fallback) {\n    // 如果是字符串，直接返回\n    if (typeof textObj === \"string\") {\n        return textObj;\n    }\n    // 如果是对象，尝试获取对应语言的文本\n    if (textObj && typeof textObj === \"object\") {\n        // 优先使用当前语言\n        if (textObj[locale]) {\n            return textObj[locale];\n        }\n        // 回退到英文\n        if (textObj.en) {\n            return textObj.en;\n        }\n        // 回退到第一个可用的值\n        const firstValue = Object.values(textObj)[0];\n        if (firstValue) {\n            return firstValue;\n        }\n    }\n    // 最终回退\n    return fallback || \"Text not available\";\n}\n/**\n * 语言切换错误处理\n * @param error - 错误对象\n * @param locale - 目标语言\n * @returns 错误处理结果\n */ function handleLanguageSwitchError(error, locale) {\n    console.error(`Language switch to ${locale} failed:`, error);\n    // 可以在这里添加用户通知逻辑\n    // 例如显示 toast 消息或回退到默认语言\n    return {\n        success: false,\n        error: error.message,\n        fallbackLocale: \"en\"\n    };\n}\n/**\n * 音频加载错误处理\n * @param error - 错误对象\n * @param audioId - 音频ID\n * @param locale - 当前语言\n * @returns 本地化的错误消息\n */ function getAudioErrorMessage(error, audioId, locale) {\n    const errorMessages = {\n        en: {\n            networkError: \"Network connection failed. Please check your internet connection.\",\n            audioNotFound: \"Audio file not found. Please try another sound.\",\n            browserNotSupported: \"Your browser does not support this audio format.\",\n            audioContextFailed: \"Failed to initialize audio system. Please refresh the page.\",\n            unknownError: \"An unexpected error occurred. Please try again.\"\n        },\n        zh: {\n            networkError: \"网络连接失败，请检查您的网络连接。\",\n            audioNotFound: \"音频文件未找到，请尝试其他音频。\",\n            browserNotSupported: \"您的浏览器不支持此音频格式。\",\n            audioContextFailed: \"音频系统初始化失败，请刷新页面。\",\n            unknownError: \"发生未知错误，请重试。\"\n        }\n    };\n    const messages = errorMessages[locale] || errorMessages.en;\n    // 根据错误类型返回相应消息\n    if (error.message.includes(\"network\") || error.message.includes(\"fetch\")) {\n        return messages.networkError;\n    }\n    if (error.message.includes(\"404\") || error.message.includes(\"not found\")) {\n        return messages.audioNotFound;\n    }\n    if (error.message.includes(\"browser\") || error.message.includes(\"support\")) {\n        return messages.browserNotSupported;\n    }\n    if (error.message.includes(\"AudioContext\") || error.message.includes(\"audio context\")) {\n        return messages.audioContextFailed;\n    }\n    return messages.unknownError;\n}\n/**\n * 创建错误边界的错误处理函数\n * @param error - 错误对象\n * @param errorInfo - 错误信息\n * @param locale - 当前语言\n */ function logErrorBoundaryError(error, errorInfo, locale) {\n    console.error(\"Error Boundary caught an error:\", {\n        error: error.message,\n        stack: error.stack,\n        componentStack: errorInfo.componentStack,\n        locale,\n        timestamp: new Date().toISOString(),\n        userAgent:  false ? 0 : \"SSR\"\n    });\n    // 在生产环境中，可以将错误发送到错误监控服务\n    if (false) {}\n}\n/**\n * 验证翻译键是否存在\n * @param t - 翻译函数\n * @param key - 翻译键\n * @returns 是否存在\n */ function hasTranslationKey(t, key) {\n    try {\n        const result = t(key);\n        return !result.includes(\"MISSING_MESSAGE\") && result !== key;\n    } catch  {\n        return false;\n    }\n}\n/**\n * 获取错误页面的本地化内容\n * @param locale - 当前语言\n * @param errorType - 错误类型\n * @returns 本地化的错误页面内容\n */ function getErrorPageContent(locale, errorType) {\n    const content = {\n        en: {\n            \"404\": {\n                title: \"Page Not Found\",\n                message: \"The page you are looking for does not exist.\",\n                action: \"Go to Homepage\"\n            },\n            \"500\": {\n                title: \"Server Error\",\n                message: \"Something went wrong on our end. Please try again later.\",\n                action: \"Refresh Page\"\n            },\n            \"network\": {\n                title: \"Network Error\",\n                message: \"Unable to connect to the server. Please check your internet connection.\",\n                action: \"Try Again\"\n            }\n        },\n        zh: {\n            \"404\": {\n                title: \"页面未找到\",\n                message: \"您访问的页面不存在。\",\n                action: \"返回首页\"\n            },\n            \"500\": {\n                title: \"服务器错误\",\n                message: \"服务器出现问题，请稍后重试。\",\n                action: \"刷新页面\"\n            },\n            \"network\": {\n                title: \"网络错误\",\n                message: \"无法连接到服务器，请检查您的网络连接。\",\n                action: \"重试\"\n            }\n        }\n    };\n    return content[locale]?.[errorType] || content.en[errorType];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/i18nHelpers.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"95df7b7bb871\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbm9pc2VzbGVlcC13ZWIvLi9zcmMvYXBwL2dsb2JhbHMuY3NzP2JlNTUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI5NWRmN2I3YmI4NzFcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/layout.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/layout.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUdNQTtBQUZpQjtBQUlSLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztrQkFDQyw0RUFBQ0M7WUFBS0MsV0FBV0wsK0pBQWU7c0JBQzdCRTs7Ozs7Ozs7Ozs7QUFJVCIsInNvdXJjZXMiOlsid2VicGFjazovL25vaXNlc2xlZXAtd2ViLy4vc3JjL2FwcC9sYXlvdXQudHN4PzU3YTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgSW50ZXIgfSBmcm9tICduZXh0L2ZvbnQvZ29vZ2xlJztcbmltcG9ydCAnLi9nbG9iYWxzLmNzcyc7XG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSk7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbD5cbiAgICAgIDxib2R5IGNsYXNzTmFtZT17aW50ZXIuY2xhc3NOYW1lfT5cbiAgICAgICAge2NoaWxkcmVufVxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJpbnRlciIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJib2R5IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/not-found.tsx":
/*!*******************************!*\
  !*** ./src/app/not-found.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/app/not-found.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=private-next-app-dir%2Fnot-found.tsx&appDir=%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
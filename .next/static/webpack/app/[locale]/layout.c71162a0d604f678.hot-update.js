/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/layout",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fshared%2FNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2F%5Blocale%5D%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2F%5Blocale%5D%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Sans_SC%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-noto-sans-sc%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSansSC%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FAudioPlayer.tsx%22%2C%22ids%22%3A%5B%22AudioPlayer%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FAudioPlayerProvider.tsx%22%2C%22ids%22%3A%5B%22AudioPlayerProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FPlayButton.tsx%22%2C%22ids%22%3A%5B%22PlayButton%22%2C%22PrimaryPlayButton%22%2C%22SecondaryPlayButton%22%2C%22GhostPlayButton%22%2C%22LargePlayButton%22%2C%22SmallPlayButton%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FProgressBar.tsx%22%2C%22ids%22%3A%5B%22ProgressBar%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FStandardPlayer.tsx%22%2C%22ids%22%3A%5B%22StandardPlayer%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FVolumeControl.tsx%22%2C%22ids%22%3A%5B%22VolumeControl%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FErrorBoundary%2FErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22ErrorBoundary%22%5D%7D&server=false!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fshared%2FNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2F%5Blocale%5D%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2F%5Blocale%5D%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Sans_SC%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-noto-sans-sc%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSansSC%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FAudioPlayer.tsx%22%2C%22ids%22%3A%5B%22AudioPlayer%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FAudioPlayerProvider.tsx%22%2C%22ids%22%3A%5B%22AudioPlayerProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FPlayButton.tsx%22%2C%22ids%22%3A%5B%22PlayButton%22%2C%22PrimaryPlayButton%22%2C%22SecondaryPlayButton%22%2C%22GhostPlayButton%22%2C%22LargePlayButton%22%2C%22SmallPlayButton%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FProgressBar.tsx%22%2C%22ids%22%3A%5B%22ProgressBar%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FStandardPlayer.tsx%22%2C%22ids%22%3A%5B%22StandardPlayer%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FVolumeControl.tsx%22%2C%22ids%22%3A%5B%22VolumeControl%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FErrorBoundary%2FErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22ErrorBoundary%22%5D%7D&server=false! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js */ \"(app-pages-browser)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"src/app/[locale]/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/[locale]/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"src/app/[locale]/layout.tsx\",\"import\":\"Noto_Sans_SC\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-noto-sans-sc\",\"display\":\"swap\"}],\"variableName\":\"notoSansSC\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/[locale]/layout.tsx\\\",\\\"import\\\":\\\"Noto_Sans_SC\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-noto-sans-sc\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"notoSansSC\\\"}\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/AudioPlayer/AudioPlayer.tsx */ \"(app-pages-browser)/./src/components/AudioPlayer/AudioPlayer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/AudioPlayer/AudioPlayerProvider.tsx */ \"(app-pages-browser)/./src/components/AudioPlayer/AudioPlayerProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/AudioPlayer/PlayButton.tsx */ \"(app-pages-browser)/./src/components/AudioPlayer/PlayButton.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/AudioPlayer/ProgressBar.tsx */ \"(app-pages-browser)/./src/components/AudioPlayer/ProgressBar.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/AudioPlayer/StandardPlayer.tsx */ \"(app-pages-browser)/./src/components/AudioPlayer/StandardPlayer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/AudioPlayer/VolumeControl.tsx */ \"(app-pages-browser)/./src/components/AudioPlayer/VolumeControl.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ErrorBoundary/ErrorBoundary.tsx */ \"(app-pages-browser)/./src/components/ErrorBoundary/ErrorBoundary.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fshared%2FNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2F%5Blocale%5D%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2F%5Blocale%5D%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Sans_SC%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-noto-sans-sc%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSansSC%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FAudioPlayer.tsx%22%2C%22ids%22%3A%5B%22AudioPlayer%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FAudioPlayerProvider.tsx%22%2C%22ids%22%3A%5B%22AudioPlayerProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FPlayButton.tsx%22%2C%22ids%22%3A%5B%22PlayButton%22%2C%22PrimaryPlayButton%22%2C%22SecondaryPlayButton%22%2C%22GhostPlayButton%22%2C%22LargePlayButton%22%2C%22SmallPlayButton%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FProgressBar.tsx%22%2C%22ids%22%3A%5B%22ProgressBar%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FStandardPlayer.tsx%22%2C%22ids%22%3A%5B%22StandardPlayer%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FVolumeControl.tsx%22%2C%22ids%22%3A%5B%22VolumeControl%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FErrorBoundary%2FErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22ErrorBoundary%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ErrorBoundary/ErrorBoundary.tsx":
/*!********************************************************!*\
  !*** ./src/components/ErrorBoundary/ErrorBoundary.tsx ***!
  \********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorBoundary: function() { return /* binding */ ErrorBoundary; },\n/* harmony export */   useErrorHandler: function() { return /* binding */ useErrorHandler; },\n/* harmony export */   withErrorBoundary: function() { return /* binding */ withErrorBoundary; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_i18nHelpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/i18nHelpers */ \"(app-pages-browser)/./src/utils/i18nHelpers.ts\");\n/* __next_internal_client_entry_do_not_use__ ErrorBoundary,withErrorBoundary,useErrorHandler,default auto */ \nvar _s = $RefreshSig$();\n\n\n/**\n * 错误边界组件\n * 捕获子组件中的JavaScript错误并显示友好的错误界面\n */ class ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n    static getDerivedStateFromError(error) {\n        // 更新 state 使下一次渲染能够显示降级后的 UI\n        return {\n            hasError: true,\n            error\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        // 记录错误信息\n        (0,_utils_i18nHelpers__WEBPACK_IMPORTED_MODULE_2__.logErrorBoundaryError)(error, errorInfo, this.props.locale || \"en\");\n        this.setState({\n            error,\n            errorInfo\n        });\n    }\n    render() {\n        if (this.state.hasError) {\n            var _this_state_errorInfo;\n            // 如果提供了自定义回退组件，使用它\n            if (this.props.fallback) {\n                return this.props.fallback;\n            }\n            // 否则显示默认错误界面\n            const locale = this.props.locale || \"en\";\n            const content = (0,_utils_i18nHelpers__WEBPACK_IMPORTED_MODULE_2__.getErrorPageContent)(locale, \"500\");\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-md w-full mx-auto text-center px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-24 h-24 mx-auto mb-6 text-gray-400 dark:text-gray-600\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                fill: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/ErrorBoundary/ErrorBoundary.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/ErrorBoundary/ErrorBoundary.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/ErrorBoundary/ErrorBoundary.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4\",\n                            children: content.title\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/ErrorBoundary/ErrorBoundary.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 dark:text-gray-400 mb-8\",\n                            children: content.message\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/ErrorBoundary/ErrorBoundary.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: this.handleRetry,\n                                    className: \"w-full px-6 py-3 bg-indigo-500 text-white rounded-lg font-medium hover:bg-indigo-600 transition-colors\",\n                                    children: content.action\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/ErrorBoundary/ErrorBoundary.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>window.location.href = locale === \"zh\" ? \"/zh\" : \"/\",\n                                    className: \"w-full px-6 py-3 bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-lg font-medium hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors\",\n                                    children: locale === \"zh\" ? \"返回首页\" : \"Go to Homepage\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/ErrorBoundary/ErrorBoundary.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/ErrorBoundary/ErrorBoundary.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 13\n                        }, this),\n                         true && this.state.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                            className: \"mt-8 text-left\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                    className: \"cursor-pointer text-sm text-gray-500 hover:text-gray-700\",\n                                    children: locale === \"zh\" ? \"错误详情 (开发模式)\" : \"Error Details (Development)\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/ErrorBoundary/ErrorBoundary.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 p-4 bg-red-50 dark:bg-red-900/20 rounded-lg text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-mono text-red-600 dark:text-red-400 mb-2\",\n                                            children: this.state.error.message\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/ErrorBoundary/ErrorBoundary.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 19\n                                        }, this),\n                                        this.state.error.stack && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                            className: \"text-xs text-red-500 dark:text-red-300 overflow-auto\",\n                                            children: this.state.error.stack\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/ErrorBoundary/ErrorBoundary.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 21\n                                        }, this),\n                                        ((_this_state_errorInfo = this.state.errorInfo) === null || _this_state_errorInfo === void 0 ? void 0 : _this_state_errorInfo.componentStack) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-semibold text-red-600 dark:text-red-400 mb-1\",\n                                                    children: \"Component Stack:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/ErrorBoundary/ErrorBoundary.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                    className: \"text-xs text-red-500 dark:text-red-300 overflow-auto\",\n                                                    children: this.state.errorInfo.componentStack\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/ErrorBoundary/ErrorBoundary.tsx\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/ErrorBoundary/ErrorBoundary.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/ErrorBoundary/ErrorBoundary.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/ErrorBoundary/ErrorBoundary.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/ErrorBoundary/ErrorBoundary.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/ErrorBoundary/ErrorBoundary.tsx\",\n                lineNumber: 59,\n                columnNumber: 9\n            }, this);\n        }\n        return this.props.children;\n    }\n    constructor(props){\n        super(props);\n        this.handleRetry = ()=>{\n            this.setState({\n                hasError: false,\n                error: undefined,\n                errorInfo: undefined\n            });\n        };\n        this.state = {\n            hasError: false\n        };\n    }\n}\n/**\n * 高阶组件：为组件添加错误边界\n */ function withErrorBoundary(Component, locale, fallback) {\n    return function WrappedComponent(props) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorBoundary, {\n            locale: locale,\n            fallback: fallback,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...props\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/ErrorBoundary/ErrorBoundary.tsx\",\n                lineNumber: 143,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/ErrorBoundary/ErrorBoundary.tsx\",\n            lineNumber: 142,\n            columnNumber: 7\n        }, this);\n    };\n}\n/**\n * Hook：在函数组件中使用错误边界\n */ function useErrorHandler(locale) {\n    _s();\n    const [error, setError] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(null);\n    const resetError = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback(()=>{\n        setError(null);\n    }, []);\n    const handleError = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback((error)=>{\n        console.error(\"Error caught by useErrorHandler:\", error);\n        setError(error);\n    }, []);\n    // 如果有错误，抛出它让错误边界捕获\n    if (error) {\n        throw error;\n    }\n    return {\n        handleError,\n        resetError\n    };\n}\n_s(useErrorHandler, \"jPWRr4+uYhIn4gdozpLpv/p1Ob8=\");\n/* harmony default export */ __webpack_exports__[\"default\"] = (ErrorBoundary);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ErrorBoundary/ErrorBoundary.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/i18nHelpers.ts":
/*!**********************************!*\
  !*** ./src/utils/i18nHelpers.ts ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAudioErrorMessage: function() { return /* binding */ getAudioErrorMessage; },\n/* harmony export */   getErrorPageContent: function() { return /* binding */ getErrorPageContent; },\n/* harmony export */   getLocalizedText: function() { return /* binding */ getLocalizedText; },\n/* harmony export */   handleLanguageSwitchError: function() { return /* binding */ handleLanguageSwitchError; },\n/* harmony export */   hasTranslationKey: function() { return /* binding */ hasTranslationKey; },\n/* harmony export */   logErrorBoundaryError: function() { return /* binding */ logErrorBoundaryError; },\n/* harmony export */   safeTranslate: function() { return /* binding */ safeTranslate; }\n/* harmony export */ });\n/**\n * 国际化辅助工具\n * 提供翻译键缺失时的优雅降级机制\n */ /**\n * 安全的翻译函数，提供回退机制\n * @param t - next-intl 翻译函数\n * @param key - 翻译键\n * @param fallback - 回退文本\n * @param params - 翻译参数\n * @returns 翻译文本或回退文本\n */ function safeTranslate(t, key, fallback, params) {\n    try {\n        const result = t(key, params);\n        // 检查是否返回了缺失消息标识\n        if (result.includes(\"MISSING_MESSAGE\") || result === key) {\n            console.warn(\"Translation missing for key: \".concat(key));\n            return fallback || key;\n        }\n        return result;\n    } catch (error) {\n        console.error(\"Translation error for key: \".concat(key), error);\n        return fallback || key;\n    }\n}\n/**\n * 获取本地化文本的安全方法\n * @param textObj - 包含多语言文本的对象\n * @param locale - 当前语言环境\n * @param fallback - 回退文本\n * @returns 本地化文本\n */ function getLocalizedText(textObj, locale, fallback) {\n    // 如果是字符串，直接返回\n    if (typeof textObj === \"string\") {\n        return textObj;\n    }\n    // 如果是对象，尝试获取对应语言的文本\n    if (textObj && typeof textObj === \"object\") {\n        // 优先使用当前语言\n        if (textObj[locale]) {\n            return textObj[locale];\n        }\n        // 回退到英文\n        if (textObj.en) {\n            return textObj.en;\n        }\n        // 回退到第一个可用的值\n        const firstValue = Object.values(textObj)[0];\n        if (firstValue) {\n            return firstValue;\n        }\n    }\n    // 最终回退\n    return fallback || \"Text not available\";\n}\n/**\n * 语言切换错误处理\n * @param error - 错误对象\n * @param locale - 目标语言\n * @returns 错误处理结果\n */ function handleLanguageSwitchError(error, locale) {\n    console.error(\"Language switch to \".concat(locale, \" failed:\"), error);\n    // 可以在这里添加用户通知逻辑\n    // 例如显示 toast 消息或回退到默认语言\n    return {\n        success: false,\n        error: error.message,\n        fallbackLocale: \"en\"\n    };\n}\n/**\n * 音频加载错误处理\n * @param error - 错误对象\n * @param audioId - 音频ID\n * @param locale - 当前语言\n * @returns 本地化的错误消息\n */ function getAudioErrorMessage(error, audioId, locale) {\n    const errorMessages = {\n        en: {\n            networkError: \"Network connection failed. Please check your internet connection.\",\n            audioNotFound: \"Audio file not found. Please try another sound.\",\n            browserNotSupported: \"Your browser does not support this audio format.\",\n            audioContextFailed: \"Failed to initialize audio system. Please refresh the page.\",\n            unknownError: \"An unexpected error occurred. Please try again.\"\n        },\n        zh: {\n            networkError: \"网络连接失败，请检查您的网络连接。\",\n            audioNotFound: \"音频文件未找到，请尝试其他音频。\",\n            browserNotSupported: \"您的浏览器不支持此音频格式。\",\n            audioContextFailed: \"音频系统初始化失败，请刷新页面。\",\n            unknownError: \"发生未知错误，请重试。\"\n        }\n    };\n    const messages = errorMessages[locale] || errorMessages.en;\n    // 根据错误类型返回相应消息\n    if (error.message.includes(\"network\") || error.message.includes(\"fetch\")) {\n        return messages.networkError;\n    }\n    if (error.message.includes(\"404\") || error.message.includes(\"not found\")) {\n        return messages.audioNotFound;\n    }\n    if (error.message.includes(\"browser\") || error.message.includes(\"support\")) {\n        return messages.browserNotSupported;\n    }\n    if (error.message.includes(\"AudioContext\") || error.message.includes(\"audio context\")) {\n        return messages.audioContextFailed;\n    }\n    return messages.unknownError;\n}\n/**\n * 创建错误边界的错误处理函数\n * @param error - 错误对象\n * @param errorInfo - 错误信息\n * @param locale - 当前语言\n */ function logErrorBoundaryError(error, errorInfo, locale) {\n    console.error(\"Error Boundary caught an error:\", {\n        error: error.message,\n        stack: error.stack,\n        componentStack: errorInfo.componentStack,\n        locale,\n        timestamp: new Date().toISOString(),\n        userAgent:  true ? window.navigator.userAgent : 0\n    });\n    // 在生产环境中，可以将错误发送到错误监控服务\n    if (false) {}\n}\n/**\n * 验证翻译键是否存在\n * @param t - 翻译函数\n * @param key - 翻译键\n * @returns 是否存在\n */ function hasTranslationKey(t, key) {\n    try {\n        const result = t(key);\n        return !result.includes(\"MISSING_MESSAGE\") && result !== key;\n    } catch (e) {\n        return false;\n    }\n}\n/**\n * 获取错误页面的本地化内容\n * @param locale - 当前语言\n * @param errorType - 错误类型\n * @returns 本地化的错误页面内容\n */ function getErrorPageContent(locale, errorType) {\n    var _content_locale;\n    const content = {\n        en: {\n            \"404\": {\n                title: \"Page Not Found\",\n                message: \"The page you are looking for does not exist.\",\n                action: \"Go to Homepage\"\n            },\n            \"500\": {\n                title: \"Server Error\",\n                message: \"Something went wrong on our end. Please try again later.\",\n                action: \"Refresh Page\"\n            },\n            \"network\": {\n                title: \"Network Error\",\n                message: \"Unable to connect to the server. Please check your internet connection.\",\n                action: \"Try Again\"\n            }\n        },\n        zh: {\n            \"404\": {\n                title: \"页面未找到\",\n                message: \"您访问的页面不存在。\",\n                action: \"返回首页\"\n            },\n            \"500\": {\n                title: \"服务器错误\",\n                message: \"服务器出现问题，请稍后重试。\",\n                action: \"刷新页面\"\n            },\n            \"network\": {\n                title: \"网络错误\",\n                message: \"无法连接到服务器，请检查您的网络连接。\",\n                action: \"重试\"\n            }\n        }\n    };\n    return ((_content_locale = content[locale]) === null || _content_locale === void 0 ? void 0 : _content_locale[errorType]) || content.en[errorType];\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/i18nHelpers.ts\n"));

/***/ })

});
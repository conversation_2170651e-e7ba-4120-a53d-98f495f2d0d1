"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/sounds/[category]/page",{

/***/ "(app-pages-browser)/./src/components/LanguageSelector/LanguageSelector.tsx":
/*!**************************************************************!*\
  !*** ./src/components/LanguageSelector/LanguageSelector.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LanguageSelector; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst languages = [\n    {\n        code: \"zh\",\n        name: \"中文\",\n        flag: \"\\uD83C\\uDDE8\\uD83C\\uDDF3\"\n    },\n    {\n        code: \"en\",\n        name: \"English\",\n        flag: \"\\uD83C\\uDDFA\\uD83C\\uDDF8\"\n    }\n];\nfunction LanguageSelector(param) {\n    let { variant = \"header\", className = \"\" } = param;\n    _s();\n    const locale = (0,next_intl__WEBPACK_IMPORTED_MODULE_3__.useLocale)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    const [isPending, startTransition] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useTransition)();\n    const handleLanguageChange = (newLocale)=>{\n        if (newLocale === locale) return;\n        startTransition(()=>{\n            try {\n                // 替换当前路径中的语言代码\n                let newPathname = pathname;\n                // 处理不同的路径格式\n                if (pathname.startsWith(\"/\".concat(locale, \"/\"))) {\n                    // 路径格式: /zh/sounds -> /en/sounds\n                    newPathname = pathname.replace(\"/\".concat(locale, \"/\"), \"/\".concat(newLocale, \"/\"));\n                } else if (pathname === \"/\".concat(locale)) {\n                    // 路径格式: /zh -> /en\n                    newPathname = \"/\".concat(newLocale);\n                } else if (pathname === \"/\" && newLocale !== \"en\") {\n                    // 路径格式: / -> /zh\n                    newPathname = \"/\".concat(newLocale);\n                } else if (pathname.startsWith(\"/\") && locale === \"en\" && newLocale !== \"en\") {\n                    // 路径格式: /sounds -> /zh/sounds\n                    newPathname = \"/\".concat(newLocale).concat(pathname);\n                } else if (pathname.startsWith(\"/\".concat(locale))) {\n                    // 其他情况的处理\n                    newPathname = pathname.replace(\"/\".concat(locale), \"/\".concat(newLocale));\n                }\n                // 确保新路径格式正确\n                if (newLocale === \"en\" && newPathname.startsWith(\"/en/\")) {\n                    newPathname = newPathname.replace(\"/en/\", \"/\");\n                }\n                router.push(newPathname);\n            } catch (error) {\n                console.error(\"Language switch failed:\", error);\n                // 回退到默认页面\n                const fallbackPath = newLocale === \"zh\" ? \"/zh\" : \"/\";\n                router.push(fallbackPath);\n            }\n        });\n    };\n    if (variant === \"footer\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center \".concat(className),\n            children: languages.map((lang)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>handleLanguageChange(lang.code),\n                    disabled: isPending,\n                    className: \"text-sm mx-1 px-2 py-1 rounded transition-colors \".concat(locale === lang.code ? \"bg-indigo-600 text-white\" : \"hover:text-white\", \" \").concat(isPending ? \"opacity-50 cursor-not-allowed\" : \"\"),\n                    children: [\n                        lang.flag,\n                        \" \",\n                        lang.name\n                    ]\n                }, lang.code, true, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/LanguageSelector/LanguageSelector.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/LanguageSelector/LanguageSelector.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n        value: locale,\n        onChange: (e)=>handleLanguageChange(e.target.value),\n        disabled: isPending,\n        className: \"bg-white border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 \".concat(isPending ? \"opacity-50 cursor-not-allowed\" : \"\", \" \").concat(className),\n        children: languages.map((lang)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                value: lang.code,\n                children: [\n                    lang.flag,\n                    \" \",\n                    lang.name\n                ]\n            }, lang.code, true, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/LanguageSelector/LanguageSelector.tsx\",\n                lineNumber: 104,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/LanguageSelector/LanguageSelector.tsx\",\n        lineNumber: 95,\n        columnNumber: 5\n    }, this);\n}\n_s(LanguageSelector, \"LGMBOyrqF9qMPNKsTVLVYBjVoMQ=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_3__.useLocale,\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname,\n        react__WEBPACK_IMPORTED_MODULE_2__.useTransition\n    ];\n});\n_c = LanguageSelector;\nvar _c;\n$RefreshReg$(_c, \"LanguageSelector\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/LanguageSelector/LanguageSelector.tsx\n"));

/***/ })

});
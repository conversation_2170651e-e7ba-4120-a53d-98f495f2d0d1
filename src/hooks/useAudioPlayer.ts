import { useEffect, useRef, useCallback } from 'react';
import { Howl, Howler } from 'howler';
import { useAudioStore } from '@/store/audioStore';
import { MultilingualAudioItem } from '@/types/audio';
import { getAudioUrl } from '@/config/audio';

interface UseAudioPlayerReturn {
  play: (sound?: MultilingualAudioItem) => void;
  pause: () => void;
  stop: () => void;
  setVolume: (volume: number) => void;
  setLoop: (loop: boolean) => void;
  seek: (position: number) => void;
  isPlaying: boolean;
  isPaused: boolean;
  isLoading: boolean;
  currentTime: number;
  duration: number;
  volume: number;
  isLooping: boolean;
  error: string | null;
}

export const useAudioPlayer = (): UseAudioPlayerReturn => {
  const howlRef = useRef<Howl | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  
  const {
    currentSound,
    playState,
    userVolume,
    setCurrentSound,
    updatePlayState,
    setUserVolume,
  } = useAudioStore();

  // 清理定时器
  const clearProgressInterval = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }, []);

  // 开始进度更新定时器
  const startProgressInterval = useCallback(() => {
    clearProgressInterval();
    intervalRef.current = setInterval(() => {
      if (howlRef.current && howlRef.current.playing()) {
        const currentTime = howlRef.current.seek() as number;
        updatePlayState({ currentTime });
      }
    }, 1000);
  }, [updatePlayState, clearProgressInterval]);

  // 播放音频
  const play = useCallback((sound?: MultilingualAudioItem) => {
    try {
      console.log('🎵 播放音频被调用:', { sound: sound?.title, currentSound: currentSound?.title });

      // 如果传入了新的音频，先停止当前播放
      if (sound && sound.id !== currentSound?.id) {
        console.log('🔄 切换到新音频:', sound.title);

        console.log('🚨🚨🚨 DEBUG: 准备调用 stop() 函数');
        console.log('🛑 调用 stop() 函数');
        stop();
        console.log('✅ stop() 函数执行完成');

        // 先设置当前音频，确保UI能够显示
        setCurrentSound(sound);

        // 使用CDN配置生成音频URL
        console.log('🚨 useAudioPlayer: 准备调用 getAudioUrl', { category: sound.category, filename: sound.filename });
        const audioUrl = getAudioUrl(sound.category, sound.filename);
        console.log('🚨 useAudioPlayer: getAudioUrl 返回结果:', audioUrl);

        // 创建新的 Howl 实例
        howlRef.current = new Howl({
          src: [audioUrl],
          volume: userVolume,
          loop: playState.isLooping,
          onload: () => {
            updatePlayState({
              isLoading: false,
              duration: howlRef.current?.duration() || 0,
              error: undefined,
            });
          },
          onplay: () => {
            updatePlayState({
              isPlaying: true,
              isPaused: false,
              error: undefined,
            });
            startProgressInterval();
          },
          onpause: () => {
            updatePlayState({
              isPlaying: false,
              isPaused: true,
            });
            clearProgressInterval();
          },
          onstop: () => {
            updatePlayState({
              isPlaying: false,
              isPaused: false,
              currentTime: 0,
            });
            clearProgressInterval();
          },
          onend: () => {
            updatePlayState({
              isPlaying: false,
              isPaused: false,
              currentTime: 0,
            });
            clearProgressInterval();
          },
          onloaderror: (id, error) => {
            console.error('音频加载失败:', error);
            updatePlayState({
              isLoading: false,
              error: '音频加载失败',
            });
          },
          onplayerror: (id, error) => {
            console.error('音频播放失败:', error);
            updatePlayState({
              isPlaying: false,
              error: '音频播放失败',
            });
          },
        });

        updatePlayState({ isLoading: true });
      }

      // 播放音频
      if (howlRef.current) {
        if (playState.isPaused) {
          howlRef.current.play();
        } else {
          howlRef.current.play();
        }
      }
    } catch (error) {
      console.error('播放音频时发生错误:', error);
      updatePlayState({
        error: '播放失败',
        isLoading: false,
      });
    }
  }, [playState.isLooping, playState.isPaused, userVolume, setCurrentSound, updatePlayState, startProgressInterval]);

  // 暂停音频
  const pause = useCallback(() => {
    if (howlRef.current && howlRef.current.playing()) {
      howlRef.current.pause();
    }
  }, []);

  // 停止音频
  const stop = useCallback(() => {
    if (howlRef.current) {
      howlRef.current.stop();
      howlRef.current.unload();
      howlRef.current = null;
    }
    clearProgressInterval();
    updatePlayState({
      isPlaying: false,
      isPaused: false,
      currentTime: 0,
    });
  }, [clearProgressInterval, updatePlayState]);

  // 设置音量
  const setVolume = useCallback((volume: number) => {
    const clampedVolume = Math.max(0, Math.min(1, volume));
    setUserVolume(clampedVolume);
    updatePlayState({ volume: clampedVolume });
    
    if (howlRef.current) {
      howlRef.current.volume(clampedVolume);
    }
    
    // 设置全局音量
    Howler.volume(clampedVolume);
  }, [setUserVolume, updatePlayState]);

  // 设置循环播放
  const setLoop = useCallback((loop: boolean) => {
    updatePlayState({ isLooping: loop });
    if (howlRef.current) {
      howlRef.current.loop(loop);
    }
  }, [updatePlayState]);

  // 跳转到指定位置
  const seek = useCallback((position: number) => {
    if (howlRef.current) {
      howlRef.current.seek(position);
      updatePlayState({ currentTime: position });
    }
  }, [updatePlayState]);

  // 组件卸载时清理资源
  useEffect(() => {
    return () => {
      stop();
    };
  }, [stop]);

  // 监听音量变化
  useEffect(() => {
    if (howlRef.current && playState.volume !== userVolume) {
      howlRef.current.volume(userVolume);
    }
  }, [userVolume, playState.volume]);

  return {
    play,
    pause,
    stop,
    setVolume,
    setLoop,
    seek,
    isPlaying: playState.isPlaying,
    isPaused: playState.isPaused,
    isLoading: playState.isLoading,
    currentTime: playState.currentTime,
    duration: playState.duration,
    volume: playState.volume,
    isLooping: playState.isLooping,
    error: playState.error || null,
  };
};

'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { logErrorBoundaryError, getErrorPageContent } from '@/utils/i18nHelpers';

interface Props {
  children: ReactNode;
  locale?: string;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

/**
 * 错误边界组件
 * 捕获子组件中的JavaScript错误并显示友好的错误界面
 */
export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // 更新 state 使下一次渲染能够显示降级后的 UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // 记录错误信息
    logErrorBoundaryError(error, errorInfo, this.props.locale || 'en');
    
    this.setState({
      error,
      errorInfo
    });
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      // 如果提供了自定义回退组件，使用它
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // 否则显示默认错误界面
      const locale = this.props.locale || 'en';
      const content = getErrorPageContent(locale, '500');

      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
          <div className="max-w-md w-full mx-auto text-center px-4">
            {/* 错误图标 */}
            <div className="w-24 h-24 mx-auto mb-6 text-gray-400 dark:text-gray-600">
              <svg fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
            </div>

            {/* 错误标题 */}
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">
              {content.title}
            </h1>

            {/* 错误消息 */}
            <p className="text-gray-600 dark:text-gray-400 mb-8">
              {content.message}
            </p>

            {/* 操作按钮 */}
            <div className="space-y-4">
              <button
                onClick={this.handleRetry}
                className="w-full px-6 py-3 bg-indigo-500 text-white rounded-lg font-medium hover:bg-indigo-600 transition-colors"
              >
                {content.action}
              </button>

              <button
                onClick={() => window.location.href = locale === 'zh' ? '/zh' : '/'}
                className="w-full px-6 py-3 bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-lg font-medium hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
              >
                {locale === 'zh' ? '返回首页' : 'Go to Homepage'}
              </button>
            </div>

            {/* 开发环境下显示错误详情 */}
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details className="mt-8 text-left">
                <summary className="cursor-pointer text-sm text-gray-500 hover:text-gray-700">
                  {locale === 'zh' ? '错误详情 (开发模式)' : 'Error Details (Development)'}
                </summary>
                <div className="mt-4 p-4 bg-red-50 dark:bg-red-900/20 rounded-lg text-sm">
                  <div className="font-mono text-red-600 dark:text-red-400 mb-2">
                    {this.state.error.message}
                  </div>
                  {this.state.error.stack && (
                    <pre className="text-xs text-red-500 dark:text-red-300 overflow-auto">
                      {this.state.error.stack}
                    </pre>
                  )}
                  {this.state.errorInfo?.componentStack && (
                    <div className="mt-2">
                      <div className="font-semibold text-red-600 dark:text-red-400 mb-1">
                        Component Stack:
                      </div>
                      <pre className="text-xs text-red-500 dark:text-red-300 overflow-auto">
                        {this.state.errorInfo.componentStack}
                      </pre>
                    </div>
                  )}
                </div>
              </details>
            )}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * 高阶组件：为组件添加错误边界
 */
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  locale?: string,
  fallback?: ReactNode
) {
  return function WrappedComponent(props: P) {
    return (
      <ErrorBoundary locale={locale} fallback={fallback}>
        <Component {...props} />
      </ErrorBoundary>
    );
  };
}

/**
 * Hook：在函数组件中使用错误边界
 */
export function useErrorHandler(locale?: string) {
  const [error, setError] = React.useState<Error | null>(null);

  const resetError = React.useCallback(() => {
    setError(null);
  }, []);

  const handleError = React.useCallback((error: Error) => {
    console.error('Error caught by useErrorHandler:', error);
    setError(error);
  }, []);

  // 如果有错误，抛出它让错误边界捕获
  if (error) {
    throw error;
  }

  return { handleError, resetError };
}

export default ErrorBoundary;

'use client';

import { useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { clsx } from 'clsx';
import { motion, AnimatePresence } from 'framer-motion';
import { useAudioStore } from '@/store/audioStore';
import { useAudioPlayer } from '@/hooks/useAudioPlayer';
import { PlayButton } from './PlayButton';
import { VolumeControl } from './VolumeControl';
import { ProgressBar } from './ProgressBar';
import { TimerDisplay } from '../Timer/TimerDisplay';
import { 
  PlayIcon, 
  PauseIcon, 
  StopIcon,
  ClockIcon,
  SpeakerWaveIcon,
  MoonIcon,
  XMarkIcon,
  MinusIcon
} from '@heroicons/react/24/outline';

interface StandardPlayerProps {
  className?: string;
  position?: 'bottom' | 'top' | 'floating';
  showMixingButton?: boolean;
  showSleepModeButton?: boolean;
  autoHide?: boolean;
  autoHideDelay?: number;
}

export function StandardPlayer({
  className,
  position = 'bottom',
  showMixingButton = true,
  showSleepModeButton = true,
  autoHide = false,
  autoHideDelay = 5000,
}: StandardPlayerProps) {
  const t = useTranslations('audioPlayer');
  const tCommon = useTranslations('common');
  
  const {
    currentSound,
    playerUI,
    favorites,
    setPlayerVisible,
    setPlayerMode,
    togglePlayerMinimized,
    setTimerPanelVisible,
    setMixingPanelVisible,
    addToFavorites,
    removeFromFavorites,
  } = useAudioStore();

  const {
    play,
    pause,
    stop,
    setVolume,
    seek,
    isPlaying,
    isPaused,
    isLoading,
    currentTime,
    duration,
    volume,
    error,
  } = useAudioPlayer();

  // 当有音频播放时自动显示播放器
  useEffect(() => {
    if (currentSound && !playerUI.isVisible) {
      setPlayerVisible(true);
    }
  }, [currentSound, playerUI.isVisible, setPlayerVisible]);

  // 自动隐藏功能
  useEffect(() => {
    if (autoHide && !isPlaying && playerUI.isVisible) {
      const timer = setTimeout(() => {
        setPlayerVisible(false);
      }, autoHideDelay);

      return () => clearTimeout(timer);
    }
    return undefined;
  }, [autoHide, isPlaying, playerUI.isVisible, autoHideDelay, setPlayerVisible]);

  // 播放控制处理
  const handlePlay = () => {
    if (currentSound) {
      play();
    }
  };

  const handlePause = () => {
    pause();
  };

  const handleStop = () => {
    stop();
    setPlayerVisible(false);
  };

  const handleVolumeChange = (newVolume: number) => {
    setVolume(newVolume);
  };

  const handleSeek = (position: number) => {
    seek(position);
  };

  // 收藏控制
  const isFavorite = currentSound ? favorites.includes(currentSound.id) : false;
  const handleToggleFavorite = () => {
    if (currentSound) {
      if (isFavorite) {
        removeFromFavorites(currentSound.id);
      } else {
        addToFavorites(currentSound.id);
      }
    }
  };

  // 面板控制
  const handleTimerClick = () => {
    setTimerPanelVisible(!playerUI.showTimerPanel);
  };

  const handleMixingClick = () => {
    setMixingPanelVisible(!playerUI.showMixingPanel);
  };

  const handleSleepModeClick = () => {
    setPlayerMode('sleep');
  };

  const handleMinimize = () => {
    togglePlayerMinimized();
  };

  const handleClose = () => {
    stop();
    setPlayerVisible(false);
  };

  // 位置样式 - 响应式优化
  const positionClasses = {
    bottom: 'fixed bottom-0 left-0 right-0 z-50',
    top: 'fixed top-0 left-0 right-0 z-50',
    floating: 'fixed bottom-4 left-2 right-2 sm:left-4 sm:right-4 z-50 max-w-4xl mx-auto',
  };

  // 动画变体
  const playerVariants = {
    hidden: {
      y: position === 'bottom' ? 100 : position === 'top' ? -100 : 20,
      opacity: 0,
    },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 30,
      },
    },
    exit: {
      y: position === 'bottom' ? 100 : position === 'top' ? -100 : 20,
      opacity: 0,
      transition: {
        duration: 0.2,
      },
    },
  };

  const minimizedVariants = {
    normal: { height: 'auto' },
    minimized: { 
      height: 60,
      transition: { duration: 0.3 }
    },
  };

  // 调试信息
  console.log('🎵 StandardPlayer 渲染检查:', {
    isVisible: playerUI.isVisible,
    currentSound: currentSound?.title,
    shouldRender: playerUI.isVisible && currentSound
  });

  if (!playerUI.isVisible || !currentSound) {
    console.log('❌ StandardPlayer 不渲染 - isVisible:', playerUI.isVisible, 'currentSound:', !!currentSound);
    return null;
  }

  console.log('✅ StandardPlayer 正在渲染');

  return (
    <AnimatePresence>
      <motion.div
        initial="hidden"
        animate="visible"
        exit="exit"
        variants={playerVariants}
        className={clsx(
          positionClasses[position],
          'bg-white/95 dark:bg-gray-900/95 backdrop-blur-md',
          'border-t border-gray-200 dark:border-gray-700',
          position === 'floating' && 'rounded-lg border shadow-xl',
          className
        )}
        data-testid="standard-player"
      >
        <motion.div
          animate={playerUI.isMinimized ? 'minimized' : 'normal'}
          variants={minimizedVariants}
          className="overflow-hidden"
        >
          {/* 主播放器内容 */}
          <div className="px-3 sm:px-4 py-3">
            <div className="flex items-center gap-2 sm:gap-4">
              {/* 音频信息 */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-3">
                  {/* 播放控制按钮 */}
                  <div className="flex items-center gap-1 sm:gap-2">
                    <PlayButton
                      isPlaying={isPlaying}
                      isLoading={isLoading}
                      onPlay={handlePlay}
                      onPause={handlePause}
                      size="md"
                      variant="primary"
                      disabled={!currentSound}
                    />
                    
                    <button
                      onClick={handleStop}
                      disabled={!currentSound}
                      className={clsx(
                        'p-2 rounded-full transition-colors',
                        'hover:bg-gray-100 dark:hover:bg-gray-800',
                        'focus:outline-none focus:ring-2 focus:ring-amber-500',
                        'disabled:opacity-50 disabled:cursor-not-allowed',
                        'text-gray-600 dark:text-gray-400'
                      )}
                      aria-label={t('controls.stop')}
                      title={t('controls.stop')}
                    >
                      <StopIcon className="w-5 h-5" />
                    </button>
                  </div>

                  {/* 音频信息显示 */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                          {currentSound.title.zh || currentSound.title.en}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400 truncate">
                          {currentSound.description?.zh || currentSound.description?.en}
                        </div>
                      </div>

                      {/* 定时器显示 - 紧凑模式 */}
                      <div className="ml-2">
                        <TimerDisplay variant="compact" showProgress={false} />
                      </div>
                    </div>
                  </div>
                </div>

                {/* 进度条 - 只在非最小化状态显示 */}
                {!playerUI.isMinimized && (
                  <div className="mt-3">
                    <ProgressBar
                      currentTime={currentTime}
                      duration={duration}
                      onSeek={handleSeek}
                      isLoading={isLoading}
                      showTime={true}
                      size="md"
                      disabled={!currentSound}
                    />
                  </div>
                )}
              </div>

              {/* 右侧控制区域 */}
              <div className="flex items-center gap-1 sm:gap-2">
                {/* 音量控制 */}
                <div className="hidden sm:block">
                  <VolumeControl
                    volume={volume}
                    onVolumeChange={handleVolumeChange}
                    size="sm"
                    showIcon={true}
                    disabled={!currentSound}
                  />
                </div>

                {/* 功能按钮 - 只在非最小化状态显示 */}
                {!playerUI.isMinimized && (
                  <>
                    {/* 定时器按钮 - 在小屏幕上隐藏 */}
                    <button
                      onClick={handleTimerClick}
                      className={clsx(
                        'hidden sm:block p-2 rounded-full transition-colors',
                        'hover:bg-gray-100 dark:hover:bg-gray-800',
                        'focus:outline-none focus:ring-2 focus:ring-amber-500',
                        playerUI.showTimerPanel
                          ? 'text-amber-600 bg-amber-50 dark:bg-amber-900/20'
                          : 'text-gray-600 dark:text-gray-400'
                      )}
                      aria-label={t('timer.setTimer')}
                      title={t('timer.setTimer')}
                    >
                      <ClockIcon className="w-5 h-5" />
                    </button>

                    {/* 混音按钮 - 在小屏幕上隐藏 */}
                    {showMixingButton && (
                      <button
                        onClick={handleMixingClick}
                        className={clsx(
                          'hidden sm:block p-2 rounded-full transition-colors',
                          'hover:bg-gray-100 dark:hover:bg-gray-800',
                          'focus:outline-none focus:ring-2 focus:ring-amber-500',
                          playerUI.showMixingPanel
                            ? 'text-amber-600 bg-amber-50 dark:bg-amber-900/20'
                            : 'text-gray-600 dark:text-gray-400'
                        )}
                        aria-label={t('mixing.title')}
                        title={t('mixing.title')}
                      >
                        <SpeakerWaveIcon className="w-5 h-5" />
                      </button>
                    )}

                    {/* 睡眠模式按钮 */}
                    {showSleepModeButton && (
                      <button
                        onClick={handleSleepModeClick}
                        data-testid="sleep-mode-button"
                        className={clsx(
                          'p-2 rounded-full transition-colors',
                          'hover:bg-gray-100 dark:hover:bg-gray-800',
                          'focus:outline-none focus:ring-2 focus:ring-amber-500',
                          'text-gray-600 dark:text-gray-400'
                        )}
                        aria-label={t('modes.switchToSleep')}
                        title={t('modes.switchToSleep')}
                      >
                        <MoonIcon className="w-5 h-5" />
                      </button>
                    )}
                  </>
                )}

                {/* 最小化/关闭按钮 */}
                <div className="flex items-center gap-1 ml-2 border-l border-gray-200 dark:border-gray-700 pl-2">
                  <button
                    onClick={handleMinimize}
                    className={clsx(
                      'p-1.5 rounded-md transition-colors',
                      'hover:bg-gray-100 dark:hover:bg-gray-800',
                      'focus:outline-none focus:ring-2 focus:ring-amber-500',
                      'text-gray-500 dark:text-gray-400'
                    )}
                    aria-label={playerUI.isMinimized ? t('controls.maximize') : t('controls.minimize')}
                    title={playerUI.isMinimized ? t('controls.maximize') : t('controls.minimize')}
                  >
                    <MinusIcon className="w-4 h-4" />
                  </button>
                  
                  <button
                    onClick={handleClose}
                    className={clsx(
                      'p-1.5 rounded-md transition-colors',
                      'hover:bg-gray-100 dark:hover:bg-gray-800',
                      'focus:outline-none focus:ring-2 focus:ring-amber-500',
                      'text-gray-500 dark:text-gray-400'
                    )}
                    aria-label={t('controls.close')}
                    title={t('controls.close')}
                  >
                    <XMarkIcon className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}
